const http = require('http');

// Script pour tester l'accès en localhost
function testLocalhost() {
  const baseUrl = 'http://localhost:5000';
  const healthUrl = `${baseUrl}/api/health`;
  
  console.log('🧪 Test du serveur localhost');
  console.log(`🌐 URL: ${healthUrl}`);
  
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/health',
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'User-Agent': 'Test Script'
    },
    timeout: 5000
  };
  
  console.log('📡 Envoi de la requête...');
  
  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      console.log(`📊 Status: ${res.statusCode}`);
      if (res.statusCode === 200) {
        console.log('✅ Serveur localhost accessible !');
        try {
          const response = JSON.parse(data);
          console.log(`📄 Réponse: ${JSON.stringify(response, null, 2)}`);
        } catch (e) {
          console.log(`📄 Réponse brute: ${data}`);
        }
        
        // Test d'accès aux images
        testImageAccess();
      } else {
        console.log(`❌ Erreur serveur: ${res.statusCode}`);
        console.log(`📄 Réponse: ${data}`);
      }
    });
  });
  
  req.on('error', (error) => {
    console.error('❌ Erreur de connexion:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Le serveur backend n\'est pas démarré');
      console.log('   Démarrez-le avec: cd backend && node server.js');
    }
  });
  
  req.on('timeout', () => {
    console.error('❌ Timeout de la requête');
    req.destroy();
  });
  
  req.end();
}

function testImageAccess() {
  const imageUrl = 'http://localhost:5000/uploads/1758657500502-scannare.jpg';
  
  console.log('\n📸 Test d\'accès aux images');
  console.log(`🌐 URL: ${imageUrl}`);
  
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/uploads/1758657500502-scannare.jpg',
    method: 'HEAD',
    headers: {
      'Accept': 'image/*',
      'User-Agent': 'Test Script'
    },
    timeout: 5000
  };
  
  const req = http.request(options, (res) => {
    console.log(`📊 Status: ${res.statusCode}`);
    console.log(`📋 Headers: ${JSON.stringify(res.headers, null, 2)}`);
    
    if (res.statusCode === 200) {
      console.log('✅ Image accessible !');
      console.log(`📄 Content-Type: ${res.headers['content-type']}`);
      console.log(`📏 Content-Length: ${res.headers['content-length']}`);
    } else if (res.statusCode === 404) {
      console.log('❌ Image non trouvée (404)');
      console.log('💡 Vérifiez que le fichier existe dans backend/uploads/');
    } else {
      console.log(`❌ Erreur HTTP: ${res.statusCode}`);
    }
  });
  
  req.on('error', (error) => {
    console.error('❌ Erreur de connexion image:', error.message);
  });
  
  req.on('timeout', () => {
    console.error('❌ Timeout de la requête image');
    req.destroy();
  });
  
  req.end();
}

console.log('🚀 Démarrage du test localhost...\n');
testLocalhost();
