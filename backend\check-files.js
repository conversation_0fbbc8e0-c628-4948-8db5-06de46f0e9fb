const fs = require('fs');
const path = require('path');

// Script pour vérifier les fichiers dans le répertoire uploads
function checkFiles() {
  const uploadDir = path.join(__dirname, 'uploads');
  
  console.log('📁 Vérification des fichiers dans uploads');
  console.log(`📂 Répertoire: ${uploadDir}`);
  
  if (!fs.existsSync(uploadDir)) {
    console.error('❌ Le répertoire uploads n\'existe pas');
    return;
  }
  
  const files = fs.readdirSync(uploadDir);
  console.log(`📊 ${files.length} fichiers trouvés:`);
  
  files.forEach((file, index) => {
    const filePath = path.join(uploadDir, file);
    const stats = fs.statSync(filePath);
    console.log(`${index + 1}. ${file} (${stats.size} bytes)`);
  });
  
  // Vérifier spécifiquement les fichiers mentionnés dans les logs
  const testFiles = [
    '1758657500502-scannare.jpg',
    '1758657505700-Capture.jpg'
  ];
  
  console.log('\n🔍 Vérification des fichiers spécifiques:');
  testFiles.forEach(filename => {
    const filePath = path.join(uploadDir, filename);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`✅ ${filename} existe (${stats.size} bytes)`);
    } else {
      console.log(`❌ ${filename} n'existe pas`);
    }
  });
}

checkFiles();
