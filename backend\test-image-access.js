const http = require('http');
const https = require('https');

// Script pour tester l'accès aux images
function testImageAccess() {
  const baseUrl = 'https://731113948b80.ngrok-free.app';
  const testImageUrl = `${baseUrl}/uploads/1758657500502-scannare.jpg`;
  
  console.log('🧪 Test d\'accès aux images');
  console.log(`🌐 URL de base: ${baseUrl}`);
  console.log(`📸 URL de test: ${testImageUrl}`);
  
  const url = new URL(testImageUrl);
  const isHttps = url.protocol === 'https:';
  const client = isHttps ? https : http;
  
  const options = {
    hostname: url.hostname,
    port: url.port || (isHttps ? 443 : 80),
    path: url.pathname,
    method: 'HEAD',
    headers: {
      'ngrok-skip-browser-warning': 'true',
      'User-Agent': 'Test Script',
      'Accept': 'image/*'
    },
    timeout: 10000
  };
  
  console.log('📡 Envoi de la requête HEAD...');
  
  const req = client.request(options, (res) => {
    console.log(`📊 Status: ${res.statusCode}`);
    console.log(`📋 Headers: ${JSON.stringify(res.headers, null, 2)}`);
    
    if (res.statusCode === 200) {
      console.log('✅ Image accessible !');
      console.log(`📄 Content-Type: ${res.headers['content-type']}`);
      console.log(`📏 Content-Length: ${res.headers['content-length']}`);
    } else if (res.statusCode === 404) {
      console.log('❌ Image non trouvée (404)');
    } else {
      console.log(`❌ Erreur HTTP: ${res.statusCode}`);
    }
  });
  
  req.on('error', (error) => {
    console.error('❌ Erreur de connexion:', error.message);
    if (error.message.includes('ENOTFOUND')) {
      console.log('💡 L\'URL ngrok semble incorrecte ou expirée');
    }
  });
  
  req.on('timeout', () => {
    console.error('❌ Timeout de la requête');
    req.destroy();
  });
  
  req.end();
}

// Test de santé du serveur
function testServerHealth() {
  const healthUrl = 'https://731113948b80.ngrok-free.app/api/health';
  
  console.log('\n🏥 Test de santé du serveur');
  console.log(`🌐 URL: ${healthUrl}`);
  
  const url = new URL(healthUrl);
  const options = {
    hostname: url.hostname,
    port: url.port || 443,
    path: url.pathname,
    method: 'GET',
    headers: {
      'ngrok-skip-browser-warning': 'true',
      'User-Agent': 'Test Script',
      'Accept': 'application/json'
    },
    timeout: 10000
  };
  
  const req = https.request(options, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      console.log(`📊 Status: ${res.statusCode}`);
      if (res.statusCode === 200) {
        console.log('✅ Serveur accessible !');
        try {
          const response = JSON.parse(data);
          console.log(`📄 Réponse: ${JSON.stringify(response, null, 2)}`);
        } catch (e) {
          console.log(`📄 Réponse brute: ${data}`);
        }
      } else {
        console.log(`❌ Erreur serveur: ${res.statusCode}`);
        console.log(`📄 Réponse: ${data}`);
      }
      
      // Après le test de santé, tester l'accès aux images
      testImageAccess();
    });
  });
  
  req.on('error', (error) => {
    console.error('❌ Erreur de connexion au serveur:', error.message);
    console.log('💡 Vérifiez que le serveur backend est démarré et que l\'URL ngrok est correcte');
  });
  
  req.on('timeout', () => {
    console.error('❌ Timeout de la requête serveur');
    req.destroy();
  });
  
  req.end();
}

console.log('🚀 Démarrage des tests...\n');
testServerHealth();
