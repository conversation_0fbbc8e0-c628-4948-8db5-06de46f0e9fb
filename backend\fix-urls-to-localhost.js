const mongoose = require('mongoose');
const Vehicle = require('./models/Vehicle');
require('dotenv').config();

// Script pour corriger les URLs d'images vers localhost
async function fixUrlsToLocalhost() {
  try {
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connecté à MongoDB');

    // Récupérer tous les véhicules avec des images
    const vehicles = await Vehicle.find({
      $or: [
        { exteriorImageUrl: { $exists: true, $ne: null, $ne: '' } },
        { interiorImageUrl: { $exists: true, $ne: null, $ne: '' } }
      ]
    });

    console.log(`📊 ${vehicles.length} véhicules trouvés avec des images`);

    if (vehicles.length === 0) {
      console.log('Aucun véhicule à corriger');
      return;
    }

    // Afficher les URLs actuelles
    console.log('\n📋 URLs actuelles:');
    vehicles.forEach((vehicle, index) => {
      console.log(`\n${index + 1}. Véhicule ID: ${vehicle._id}`);
      if (vehicle.exteriorImageUrl) {
        console.log(`   Image extérieure: ${vehicle.exteriorImageUrl}`);
      }
      if (vehicle.interiorImageUrl) {
        console.log(`   Image intérieure: ${vehicle.interiorImageUrl}`);
      }
    });

    // Correction vers localhost
    console.log('\n🔧 Correction des URLs vers localhost...');
    
    const baseUrl = 'http://localhost:5000';
    console.log(`Base URL utilisée: ${baseUrl}`);

    let correctedCount = 0;

    for (const vehicle of vehicles) {
      let updated = false;

      // Corriger l'URL de l'image extérieure
      if (vehicle.exteriorImageUrl) {
        const filename = vehicle.exteriorImageUrl.split('/').pop();
        const newUrl = `${baseUrl}/uploads/${filename}`;
        if (vehicle.exteriorImageUrl !== newUrl) {
          vehicle.exteriorImageUrl = newUrl;
          updated = true;
        }
      }

      // Corriger l'URL de l'image intérieure
      if (vehicle.interiorImageUrl) {
        const filename = vehicle.interiorImageUrl.split('/').pop();
        const newUrl = `${baseUrl}/uploads/${filename}`;
        if (vehicle.interiorImageUrl !== newUrl) {
          vehicle.interiorImageUrl = newUrl;
          updated = true;
        }
      }

      if (updated) {
        await vehicle.save();
        correctedCount++;
        console.log(`✅ Véhicule ${vehicle._id} corrigé`);
      }
    }

    console.log(`\n🎉 ${correctedCount} véhicules corrigés`);

    // Afficher les URLs corrigées
    if (correctedCount > 0) {
      const updatedVehicles = await Vehicle.find({
        $or: [
          { exteriorImageUrl: { $exists: true, $ne: null, $ne: '' } },
          { interiorImageUrl: { $exists: true, $ne: null, $ne: '' } }
        ]
      });

      console.log('\n📋 URLs corrigées:');
      updatedVehicles.forEach((vehicle, index) => {
        console.log(`\n${index + 1}. Véhicule ID: ${vehicle._id}`);
        if (vehicle.exteriorImageUrl) {
          console.log(`   Image extérieure: ${vehicle.exteriorImageUrl}`);
        }
        if (vehicle.interiorImageUrl) {
          console.log(`   Image intérieure: ${vehicle.interiorImageUrl}`);
        }
      });
    }

    console.log(`\n🎉 Correction terminée !`);
    console.log(`💡 N'oubliez pas de:`);
    console.log(`   1. Redémarrer le serveur backend`);
    console.log(`   2. Redémarrer l'app Flutter`);
    console.log(`   3. Tester l'affichage des images`);

  } catch (error) {
    console.error('❌ Erreur lors de la correction:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Déconnecté de MongoDB');
  }
}

// Lancer la correction
fixUrlsToLocalhost();
