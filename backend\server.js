const express = require('express');
const mongoose = require('mongoose');
const http = require('http');
const socketIo = require('socket.io'); // Ajouter Socket.IO
require('dotenv').config();
const path = require('path');
const fs = require('fs');
const fsPromises = fs.promises;
const cors = require('cors');
const imageType = require('image-type');
const userRoutes = require('./routes/userRoutes');
const authRoutes = require('./routes/authRoutes');
const vehicleRoutes = require('./routes/vehicleRoutes');
const chatRoutes = require('./routes/chatRoutes');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
});

// Middleware pour ngrok
app.use((req, res, next) => {
  res.setHeader('ngrok-skip-browser-warning', 'true');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, ngrok-skip-browser-warning');
  next();
});

// Middleware CORS
app.use(cors());
app.use(express.json());

// Gestion des fichiers statiques (inchangé)
const uploadDir = path.join(__dirname, 'uploads'); // Correction: utiliser 'uploads' en minuscule
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}
app.use('/uploads', async (req, res, next) => {
  // Nettoyer le chemin en supprimant le / initial
  const filename = req.path.startsWith('/') ? req.path.substring(1) : req.path;
  const filePath = path.join(uploadDir, filename);

  console.log(`📁 Demande de fichier: ${filename}`);
  console.log(`📂 Chemin complet: ${filePath}`);

  try {
    if (!fs.existsSync(filePath)) {
      console.error(`❌ Fichier non trouvé: ${filePath}`);
      return res.status(404).json({ message: 'Fichier non trouvé' });
    }

    const fileBuffer = await fsPromises.readFile(filePath);
    let mimeType = 'application/octet-stream';

    // Détecter le type MIME
    try {
      const imgType = await imageType(fileBuffer);
      if (imgType && imgType.mime) {
        mimeType = imgType.mime;
      } else {
        // Fallback basé sur l'extension
        const ext = path.extname(filePath).toLowerCase();
        switch (ext) {
          case '.jpg':
          case '.jpeg':
            mimeType = 'image/jpeg';
            break;
          case '.png':
            mimeType = 'image/png';
            break;
          case '.gif':
            mimeType = 'image/gif';
            break;
          case '.webp':
            mimeType = 'image/webp';
            break;
        }
      }
    } catch (typeError) {
      console.warn('⚠️ Erreur détection type MIME:', typeError.message);
      // Utiliser le type par défaut
    }

    console.log(`✅ Fichier servi: ${filename}, Type: ${mimeType}, Taille: ${fileBuffer.length} bytes`);

    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Length', fileBuffer.length);
    res.setHeader('Cache-Control', 'public, max-age=31536000');
    res.send(fileBuffer);
  } catch (error) {
    console.error('❌ Erreur lors du service du fichier:', error);
    res.status(500).json({ message: 'Erreur serveur lors de la lecture du fichier', error: error.message });
  }
});

// Rendre io accessible dans les contrôleurs
app.set('io', io);

// Connexion MongoDB
mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log("MongoDB connecté"))
  .catch(err => console.log(err));

// Socket.IO : Gestion des connexions
io.on('connection', (socket) => {
  console.log('Nouvelle connexion Socket.IO:', socket.id);

  // Rejoindre une room basée sur l'ID utilisateur
  socket.on('join', (userId) => {
    socket.join(userId);
    console.log(`Utilisateur ${userId} a rejoint la room`);
  });

  // Écouter l'envoi de messages
  socket.on('sendMessage', async (data) => {
    const { senderId, receiverId, content, messageType = 'text' } = data;
    try {
      const { Conversation, Message } = require('./models/Chat');
      const User = require('./models/User');

      const receiver = await User.findById(receiverId);
      if (!receiver) {
        socket.emit('error', { message: 'Destinataire non trouvé' });
        return;
      }

      let conversation = await Conversation.findOne({
        participants: { $all: [senderId, receiverId] },
        isActive: true,
      });

      if (!conversation) {
        conversation = new Conversation({
          participants: [senderId, receiverId],
        });
        await conversation.save();
      }

      const message = new Message({
        conversationId: conversation._id,
        senderId,
        receiverId,
        content,
        messageType,
      });

      await message.save();
      await message.populate('senderId', 'nom prenom photoUrl');
      await message.populate('receiverId', 'nom prenom photoUrl');

      // Émettre le message aux deux utilisateurs
      io.to(senderId).emit('newMessage', message);
      io.to(receiverId).emit('newMessage', message);

      // Mettre à jour la conversation
      await Conversation.findByIdAndUpdate(conversation._id, {
        lastMessage: message._id,
        lastMessageTime: message.createdAt,
      });
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message via Socket.IO:', error);
      socket.emit('error', { message: 'Erreur serveur', error: error.message });
    }
  });

  socket.on('disconnect', () => {
    console.log('Déconnexion Socket.IO:', socket.id);
  });
});

// Routes existantes
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Serveur fonctionnel',
    timestamp: new Date().toISOString(),
    endpoints: {
      login: '/api/auth/login',
      register: '/api/users/register',
      chat: '/api/chat',
    },
  });
});

app.use('/api/users', userRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/vehicles', vehicleRoutes);
app.use('/api/chat', chatRoutes);

app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Erreur serveur inattendue' });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => console.log(`Serveur démarré sur le port ${PORT}`));